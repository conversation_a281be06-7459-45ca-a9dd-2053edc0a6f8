# 📦 INSTRUCCIONES DE ENTREGA - TRABAJO FINAL

## 📋 Requisitos de Entrega

### 📁 Formato del Archivo
- **Nombre del archivo:** `nombre_apellido1_TrabajoFinalMaquinasVirtuales.zip`
- **Formato:** ZIP o RAR
- **Ejemplo:** `<PERSON>_<PERSON>_TrabajoFinalMaquinasVirtuales.zip`

### 📄 Contenido Requerido

El archivo comprimido debe contener:

1. **Documento principal** (obligatorio)
   - Formato: `.txt`, `.docx` o `.pdf`
   - Contenido: Resolución detallada de todos los apartados
   - Capturas de pantalla (no fotos)
   - Explicaciones profesionales y claras

2. **Archivos complementarios** (incluidos en este proyecto)
   - Scripts de instalación
   - Archivos de configuración
   - Página web del virtual host
   - Documentación adicional

## 📝 Estructura de Entrega Recomendada

```
nombre_apellido1_TrabajoFinalMaquinasVirtuales.zip
├── 📄 Trabajo_Final_Maquinas_Virtuales_Servidores_Web.pdf
├── 📄 README.md
├── 📁 scripts/
│   ├── install_virtualbox.sh
│   ├── install_apache.sh
│   └── verificar_instalacion.sh
├── 📁 config/
│   ├── miweb.conf
│   └── miweb-ssl.conf
├── 📁 web/
│   └── index.html
├── 📁 capturas/
│   ├── virtualbox_instalacion.png
│   ├── ubuntu_instalacion.png
│   ├── guest_additions.png
│   ├── apache_funcionando.png
│   └── virtual_host_ssl.png
└── 📁 docs/
    └── instrucciones_entrega.md
```

## 📸 Capturas de Pantalla Requeridas

### 1.1 Creación de Máquina Virtual

#### VirtualBox
- ✅ Pantalla de descarga de VirtualBox
- ✅ Proceso de instalación de VirtualBox
- ✅ Instalación del Extension Pack
- ✅ Interfaz principal de VirtualBox

#### Máquina Virtual Ubuntu
- ✅ Asistente de creación de VM
- ✅ Configuración de recursos (RAM, disco)
- ✅ Proceso de instalación de Ubuntu
- ✅ Primer arranque de Ubuntu

#### Guest Additions
- ✅ Instalación de Guest Additions
- ✅ Prueba de portapapeles compartido
- ✅ Ajuste automático de resolución

### 1.2 Virtual Host

#### Apache
- ✅ Instalación de Apache
- ✅ Página por defecto de Apache
- ✅ Estado del servicio Apache

#### Virtual Host
- ✅ Configuración del virtual host
- ✅ Generación de certificado SSL
- ✅ Sitio web funcionando en HTTP
- ✅ Sitio web funcionando en HTTPS
- ✅ Redirección HTTP → HTTPS

## ✅ Lista de Verificación Pre-Entrega

### Documento Principal
- [ ] Portada con nombre del ejercicio
- [ ] Datos personales (nombre y apellidos)
- [ ] Índice o tabla de contenido
- [ ] Páginas numeradas
- [ ] Sin faltas de ortografía
- [ ] Imágenes del mismo tamaño y formato
- [ ] Fuente legible y profesional
- [ ] Máximo 2-3 colores utilizados

### Contenido Técnico
- [ ] Explicación de descarga e instalación de VirtualBox
- [ ] Proceso de creación de máquina virtual
- [ ] Instalación detallada de Ubuntu Desktop
- [ ] Verificación con comandos apt-get
- [ ] Instalación y prueba de Guest Additions
- [ ] Instalación de Apache con verificación
- [ ] Configuración completa del virtual host
- [ ] Certificado SSL funcionando
- [ ] Acceso por www.miweb.com y miweb.com

### Capturas de Pantalla
- [ ] Todas las capturas son screenshots (no fotos)
- [ ] Mantienen las mismas proporciones
- [ ] Son claras y legibles
- [ ] Complementan las explicaciones
- [ ] Muestran los procesos paso a paso

## 🚨 Criterios de Rechazo

**ATENCIÓN:** Si no se envía el ejercicio tal y como se ha solicitado **NO se corregirá**.

### Motivos de rechazo automático:
- ❌ Nombre de archivo incorrecto
- ❌ Formato de archivo no permitido
- ❌ Falta el documento principal
- ❌ Capturas de pantalla son fotografías
- ❌ Documento sin formato profesional
- ❌ Faltan apartados obligatorios
- ❌ No funciona el virtual host

## 📊 Criterios de Evaluación

### 1.1 Creación de Máquina Virtual (4 puntos)
- **VirtualBox (0.5 pts):** Instalación correcta y Extension Pack
- **Creación VM (1.5 pts):** Proceso detallado con capturas
- **Ubuntu (1.5 pts):** Instalación y verificación completa
- **Guest Additions (0.5 pts):** Funcionamiento demostrado

### 1.2 Virtual Host (5 puntos)
- **Apache (0.5 pts):** Instalación y verificación
- **Virtual Host (4.5 pts):** Configuración completa con SSL

### 1.3 Presentación (1 punto)
- **Formato:** Profesional y bien estructurado
- **Contenido:** Completo y bien explicado
- **Capturas:** Adecuadas y complementarias

## 💡 Consejos para una Entrega Exitosa

### Antes de Empezar
1. **Lee todo el enunciado** antes de comenzar
2. **Consulta dudas** con los docentes
3. **Planifica el tiempo** necesario

### Durante el Desarrollo
1. **Toma capturas** en cada paso importante
2. **Documenta todo** lo que haces
3. **Verifica** que todo funciona correctamente

### Antes de Entregar
1. **Revisa** que todos los archivos estén incluidos
2. **Comprueba** que el virtual host funciona
3. **Verifica** el formato del documento
4. **Prueba** que el archivo ZIP se abre correctamente

## 📞 Contacto y Soporte

Si tienes dudas sobre la entrega:
1. Consulta este documento primero
2. Revisa el enunciado original
3. Contacta con los docentes del curso

---

**🎯 Objetivo:** Entregar un trabajo completo, profesional y funcional que demuestre dominio de las tecnologías de virtualización y servidores web.

**⏰ Recuerda:** La calidad de la presentación y documentación es tan importante como la funcionalidad técnica.
