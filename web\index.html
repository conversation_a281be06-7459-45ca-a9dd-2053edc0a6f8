<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sitio web del trabajo final sobre máquinas virtuales y servidores web">
    <meta name="keywords" content="virtualbox, apache, ubuntu, ssl, virtual host">
    <meta name="author" content="Trabajo Final - Máquinas Virtuales">
    <title>Mi Web - Trabajo Final | Máquinas Virtuales y Servidores Web</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <!-- Estilos CSS -->
    <style>
        /* Reset básico */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* Estilos generales */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        /* Contenedor principal */
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        /* Contenido principal */
        .content {
            padding: 40px;
        }
        
        /* Tarjetas de información */
        .info-card {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 25px;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .info-card h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .info-card p {
            margin-bottom: 10px;
        }
        
        .info-card strong {
            color: #007bff;
        }
        
        /* Estado del sistema */
        .status-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 25px;
            margin: 25px 0;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .status-card h2 {
            margin-bottom: 15px;
            font-size: 1.8em;
        }
        
        .status-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        /* Lista de características */
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-item .icon {
            font-size: 2.5em;
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .feature-item h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        /* Información técnica */
        .tech-info {
            background: #2c3e50;
            color: white;
            padding: 30px;
            margin: 30px 0;
            border-radius: 8px;
        }
        
        .tech-info h3 {
            color: #3498db;
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .tech-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 5px;
        }
        
        .tech-item strong {
            color: #3498db;
        }
        
        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .footer p {
            margin-bottom: 10px;
        }
        
        /* Animaciones */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .container {
            animation: fadeInUp 0.8s ease-out;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .features-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🌐 Mi Web</h1>
            <p>Trabajo Final - Máquinas Virtuales y Servidores Web</p>
        </div>
        
        <!-- Contenido principal -->
        <div class="content">
            <!-- Estado del sistema -->
            <div class="status-card">
                <div class="status-icon">✅</div>
                <h2>Virtual Host Configurado Correctamente</h2>
                <p>El servidor Apache está funcionando con SSL habilitado</p>
            </div>
            
            <!-- Información del Virtual Host -->
            <div class="info-card">
                <h2>📋 Información del Virtual Host</h2>
                <div class="tech-grid">
                    <div class="tech-item">
                        <p><strong>Dominio Principal:</strong> www.miweb.com</p>
                    </div>
                    <div class="tech-item">
                        <p><strong>Alias:</strong> miweb.com</p>
                    </div>
                    <div class="tech-item">
                        <p><strong>Ubicación:</strong> /var/www/webs/miweb</p>
                    </div>
                    <div class="tech-item">
                        <p><strong>SSL:</strong> Habilitado ✅</p>
                    </div>
                    <div class="tech-item">
                        <p><strong>Protocolo:</strong> HTTPS (TLS 1.2+)</p>
                    </div>
                    <div class="tech-item">
                        <p><strong>Puerto HTTP:</strong> 80 → 443</p>
                    </div>
                </div>
            </div>
            
            <!-- Descripción del proyecto -->
            <div class="info-card">
                <h2>📖 Descripción del Proyecto</h2>
                <p>Este sitio web forma parte del trabajo final sobre <strong>máquinas virtuales y servidores web</strong>. 
                El proyecto demuestra la implementación completa de:</p>
                <ul style="margin: 15px 0 0 30px;">
                    <li>Instalación y configuración de VirtualBox</li>
                    <li>Creación de máquina virtual con Ubuntu Desktop</li>
                    <li>Instalación de Guest Additions</li>
                    <li>Configuración de servidor web Apache</li>
                    <li>Implementación de virtual host con SSL</li>
                </ul>
            </div>
            
            <!-- Características implementadas -->
            <div class="info-card">
                <h2>🚀 Características Implementadas</h2>
                <div class="features-list">
                    <div class="feature-item">
                        <div class="icon">🔒</div>
                        <h3>Certificado SSL</h3>
                        <p>Certificado autofirmado para conexiones HTTPS seguras</p>
                    </div>
                    <div class="feature-item">
                        <div class="icon">🔄</div>
                        <h3>Redirección HTTP → HTTPS</h3>
                        <p>Redirección automática de tráfico no seguro</p>
                    </div>
                    <div class="feature-item">
                        <div class="icon">🛡️</div>
                        <h3>Headers de Seguridad</h3>
                        <p>Configuración avanzada de headers de seguridad</p>
                    </div>
                    <div class="feature-item">
                        <div class="icon">📊</div>
                        <h3>Logs Personalizados</h3>
                        <p>Sistema de logging detallado para monitoreo</p>
                    </div>
                    <div class="feature-item">
                        <div class="icon">🌐</div>
                        <h3>Alias de Dominio</h3>
                        <p>Soporte para múltiples nombres de dominio</p>
                    </div>
                    <div class="feature-item">
                        <div class="icon">⚡</div>
                        <h3>Optimización</h3>
                        <p>Compresión y caché para mejor rendimiento</p>
                    </div>
                </div>
            </div>
            
            <!-- Información técnica -->
            <div class="tech-info">
                <h3>🔧 Información Técnica del Sistema</h3>
                <div class="tech-grid">
                    <div class="tech-item">
                        <strong>Sistema Operativo:</strong><br>
                        Ubuntu Desktop 22.04 LTS
                    </div>
                    <div class="tech-item">
                        <strong>Servidor Web:</strong><br>
                        Apache HTTP Server 2.4
                    </div>
                    <div class="tech-item">
                        <strong>Virtualización:</strong><br>
                        Oracle VirtualBox 7.0
                    </div>
                    <div class="tech-item">
                        <strong>SSL/TLS:</strong><br>
                        OpenSSL con TLS 1.2+
                    </div>
                    <div class="tech-item">
                        <strong>Módulos Apache:</strong><br>
                        mod_ssl, mod_rewrite, mod_headers
                    </div>
                    <div class="tech-item">
                        <strong>Fecha de Instalación:</strong><br>
                        <script>document.write(new Date().toLocaleDateString('es-ES'));</script>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>Trabajo Final - Máquinas Virtuales y Servidores Web</strong></p>
            <p>Configuración completada exitosamente ✅</p>
            <p style="font-size: 0.9em; opacity: 0.8;">
                © 2024 - Implementación de Virtual Host con Apache y SSL
            </p>
        </div>
    </div>
    
    <!-- JavaScript para funcionalidades adicionales -->
    <script>
        // Mostrar información adicional del navegador
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar si la conexión es segura
            if (location.protocol === 'https:') {
                console.log('✅ Conexión HTTPS establecida correctamente');
            }
            
            // Mostrar información de la fecha actual
            const fechaElements = document.querySelectorAll('.fecha-actual');
            const fechaActual = new Date().toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            fechaElements.forEach(element => {
                element.textContent = fechaActual;
            });
        });
        
        // Función para mostrar información del certificado SSL
        function mostrarInfoSSL() {
            if (location.protocol === 'https:') {
                alert('🔒 Conexión SSL activa\n\n' +
                      'Protocolo: ' + location.protocol + '\n' +
                      'Host: ' + location.hostname + '\n' +
                      'Puerto: ' + (location.port || '443'));
            } else {
                alert('⚠️ Conexión no segura\n\nSe recomienda usar HTTPS');
            }
        }
    </script>
</body>
</html>
