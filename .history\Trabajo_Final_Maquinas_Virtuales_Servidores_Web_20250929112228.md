# TRABAJO FINAL: MÁQUINAS VIRTUALES Y SERVIDORES WEB

**Nombre:** [Tu Nombre]  
**Apellidos:** [Tus Apellidos]  
**Fecha:** Diciembre 2024  

---

## ÍNDICE

1. [Introducción](#introducción)
2. [1.1 Creación de una Máquina Virtual (4 puntos)](#11-creación-de-una-máquina-virtual-4-puntos)
   - [Descarga e instalación de VirtualBox](#descarga-e-instalación-de-virtualbox)
   - [Creación de una máquina virtual](#creación-de-una-máquina-virtual)
   - [Instalación del sistema operativo](#instalación-del-sistema-operativo)
   - [Instalación de las Guest Additions](#instalación-de-las-guest-additions)
3. [1.2 Creación de un Virtual Host (5 puntos)](#12-creación-de-un-virtual-host-5-puntos)
   - [Instalación de Apache](#instalación-de-apache)
   - [Creación de un virtual host](#creación-de-un-virtual-host)
4. [Conclusiones](#conclusiones)

---

## Introducción

Este trabajo final tiene como objetivo demostrar los conocimientos adquiridos sobre máquinas virtuales y servidores web. Se realizará la instalación completa de una máquina virtual con Ubuntu Desktop, la configuración de un servidor web Apache y la creación de un virtual host con certificado SSL.

El trabajo se divide en dos partes principales:
1. **Creación y configuración de una máquina virtual** con Ubuntu Desktop
2. **Instalación y configuración de un servidor web Apache** con virtual host

---

## 1.1 Creación de una Máquina Virtual (4 puntos)

### Descarga e instalación de VirtualBox

#### ¿Qué programa de virtualización he utilizado?

Para este trabajo he utilizado **VirtualBox** de Oracle, ya que dispongo de un ordenador con sistema operativo Windows. VirtualBox es una aplicación de virtualización multiplataforma que permite ejecutar múltiples sistemas operativos de forma simultánea en una sola máquina física.

#### Proceso de descarga e instalación

**Paso 1: Descarga de VirtualBox**

1. Accedí al sitio web oficial de VirtualBox: https://www.virtualbox.org/
2. Navegué a la sección "Downloads"
3. Seleccioné la versión para Windows hosts
4. Descargué el archivo ejecutable VirtualBox-7.0.x-Win.exe

**Paso 2: Instalación de VirtualBox**

1. Ejecuté el archivo descargado como administrador
2. Seguí el asistente de instalación:
   - Acepté los términos de licencia
   - Mantuve la ubicación de instalación por defecto
   - Seleccioné todas las características para instalar
   - Confirmé la instalación de los controladores de red
3. Completé la instalación y reinicié el sistema

**Paso 3: Descarga e instalación del Extension Pack**

El Extension Pack es un paquete adicional que extiende las funcionalidades de VirtualBox, proporcionando:
- Soporte para dispositivos USB 2.0 y 3.0
- Soporte para webcam
- Cifrado de disco
- Arranque PXE para tarjetas de red Intel
- Soporte mejorado para RDP (Remote Desktop Protocol)

**Proceso de instalación del Extension Pack:**

1. Desde el sitio web de VirtualBox, descargué el archivo Oracle_VM_VirtualBox_Extension_Pack-7.0.x.vbox-extpack
2. Abrí VirtualBox
3. Fui a Archivo → Preferencias → Extensiones
4. Hice clic en el icono "+" para añadir una extensión
5. Seleccioné el archivo descargado
6. Acepté los términos de licencia
7. Proporcioné la contraseña de administrador cuando se solicitó

#### Interfaz de VirtualBox

La interfaz principal de VirtualBox se compone de:

**Panel principal:**
- **Lista de máquinas virtuales:** Muestra todas las VMs creadas
- **Panel de detalles:** Información de la VM seleccionada
- **Panel de vista previa:** Miniatura de la VM en ejecución

**Barra de herramientas:**
- **Nueva:** Crear nueva máquina virtual
- **Configuración:** Modificar configuración de la VM
- **Iniciar:** Arrancar la máquina virtual
- **Descartar:** Eliminar estado guardado

**Funcionalidades principales:**
- Gestión de máquinas virtuales
- Configuración de hardware virtual
- Gestión de instantáneas (snapshots)
- Configuración de red
- Carpetas compartidas
- Clonación de máquinas virtuales

---

### Creación de una máquina virtual

#### ¿Qué es una máquina virtual?

Una **máquina virtual (VM)** es un entorno de software que emula un sistema informático completo, permitiendo ejecutar un sistema operativo y aplicaciones como si fuera una computadora física independiente. Las máquinas virtuales se ejecutan sobre un sistema operativo anfitrión (host) y utilizan sus recursos de hardware de forma controlada.

#### Herramientas y programas necesarios

Para crear una máquina virtual necesitamos:

1. **Software de virtualización:** VirtualBox, VMware, Hyper-V, etc.
2. **Imagen ISO del sistema operativo:** Ubuntu Desktop en nuestro caso
3. **Recursos de hardware suficientes:** RAM, espacio en disco, CPU
4. **Sistema operativo anfitrión compatible**

#### Proceso de creación de la máquina virtual Ubuntu Desktop

**Paso 1: Descarga de Ubuntu Desktop**

1. Accedí al sitio oficial de Ubuntu: https://ubuntu.com/download/desktop
2. Descargué la imagen ISO de Ubuntu Desktop 22.04 LTS (ubuntu-22.04.x-desktop-amd64.iso)
3. Verifiqué la integridad del archivo descargado

**Paso 2: Creación de la máquina virtual**

1. **Inicio del asistente:**
   - Abrí VirtualBox
   - Hice clic en "Nueva" en la barra de herramientas
   - Se abrió el asistente de creación de máquina virtual

2. **Configuración básica:**
   - **Nombre:** Ubuntu-Desktop-VM
   - **Carpeta de máquina:** Mantuve la ubicación por defecto
   - **Tipo:** Linux
   - **Versión:** Ubuntu (64-bit)

3. **Asignación de memoria RAM:**
   - Asigné 4096 MB (4 GB) de RAM
   - Esta cantidad es recomendada para Ubuntu Desktop para un rendimiento óptimo

4. **Creación del disco duro virtual:**
   - Seleccioné "Crear un disco duro virtual ahora"
   - **Tipo de archivo:** VDI (VirtualBox Disk Image)
   - **Almacenamiento:** Reservado dinámicamente
   - **Tamaño:** 25 GB (suficiente para Ubuntu Desktop y aplicaciones)

**Paso 3: Configuración adicional de la máquina virtual**

Antes de instalar el sistema operativo, configuré algunos parámetros adicionales:

1. **Sistema:**
   - **Procesador:** Asigné 2 núcleos de CPU
   - **Aceleración:** Habilitado VT-x/AMD-V y paginación anidada

2. **Pantalla:**
   - **Memoria de vídeo:** 128 MB
   - **Aceleración:** Habilitado aceleración 3D

3. **Almacenamiento:**
   - Agregué la imagen ISO de Ubuntu al controlador IDE
   - Configuré el orden de arranque para iniciar desde CD/DVD

4. **Red:**
   - **Adaptador 1:** NAT (configuración por defecto)
   - Esto permite acceso a internet desde la VM

5. **Audio:**
   - **Controlador de audio:** Controlador de audio HD de Intel
   - **Controlador anfitrión:** DirectSound (Windows)

La máquina virtual quedó lista para la instalación del sistema operativo Ubuntu Desktop.

---

### Instalación del sistema operativo

#### Proceso detallado de instalación de Ubuntu Desktop

**Paso 1: Inicio de la instalación**

1. **Arranque de la máquina virtual:**
   - Seleccioné la VM "Ubuntu-Desktop-VM"
   - Hice clic en "Iniciar"
   - La VM arrancó desde la imagen ISO de Ubuntu

2. **Pantalla de bienvenida:**
   - Apareció el menú de arranque de Ubuntu
   - Seleccioné "Try or Install Ubuntu"
   - El sistema cargó el entorno live de Ubuntu

**Paso 2: Configuración inicial**

1. **Selección de idioma:**
   - Elegí "Español" como idioma del sistema
   - Esto configura el idioma de la interfaz y el teclado

2. **Configuración de teclado:**
   - Seleccioné "Spanish" como distribución de teclado
   - Probé la configuración escribiendo caracteres especiales

3. **Conexión a internet:**
   - La VM detectó automáticamente la conexión de red
   - Elegí conectarme para descargar actualizaciones durante la instalación

**Paso 3: Tipo de instalación**

1. **Opciones de instalación:**
   - Seleccioné "Instalación normal" para incluir navegador web, utilidades, software de oficina, juegos y reproductores multimedia
   - Marqué "Descargar actualizaciones mientras se instala Ubuntu"
   - Marqué "Instalar software de terceros para gráficos y hardware Wi-Fi"

2. **Tipo de instalación del disco:**
   - Seleccioné "Borrar disco e instalar Ubuntu"
   - Como es una máquina virtual, esto no afecta al sistema anfitrión
   - Confirmé la escritura de cambios al disco

**Paso 4: Configuración de usuario y ubicación**

1. **Zona horaria:**
   - Seleccioné "Madrid" como ubicación
   - Esto configura automáticamente la zona horaria Europe/Madrid

2. **Información del usuario:**
   - **Nombre completo:** Usuario Ubuntu
   - **Nombre de usuario:** ubuntu
   - **Nombre del equipo:** ubuntu-vm
   - **Contraseña:** [contraseña segura]
   - Seleccioné "Iniciar sesión automáticamente"

**Paso 5: Proceso de instalación**

1. **Copia de archivos:**
   - El instalador comenzó a copiar archivos al disco duro virtual
   - El proceso tomó aproximadamente 20-30 minutos
   - Durante la instalación se mostraron diapositivas informativas sobre Ubuntu

2. **Finalización:**
   - Una vez completada la instalación, apareció el mensaje "La instalación ha terminado"
   - Hice clic en "Reiniciar ahora"
   - Retiré la imagen ISO del controlador virtual

**Paso 6: Primer arranque**

1. **Arranque del sistema:**
   - La VM reinició y arrancó desde el disco duro
   - Apareció la pantalla de inicio de sesión de Ubuntu
   - El sistema inició sesión automáticamente

2. **Configuración inicial de Ubuntu:**
   - Completé el asistente de bienvenida
   - Configuré las cuentas online (omitido)
   - Configuré Livepatch (omitido)
   - Envío de información del sistema (deshabilitado)

#### Verificación de la instalación correcta

Para comprobar que el sistema operativo se ha instalado correctamente, ejecuté los comandos solicitados:

**Actualización de repositorios:**
```bash
sudo apt-get update
```

Este comando actualizó la lista de paquetes disponibles desde los repositorios configurados. La salida mostró que se descargaron las listas de paquetes sin errores.

**Actualización del sistema:**
```bash
sudo apt-get upgrade
```

Este comando actualizó todos los paquetes instalados a sus versiones más recientes. Se instalaron varias actualizaciones de seguridad y mejoras.

**Instalación de herramientas de red:**
```bash
sudo apt-get install net-tools
```

Este comando instaló las herramientas de red tradicionales como ifconfig, netstat, route, etc. La instalación se completó exitosamente.

**Verificaciones adicionales realizadas:**

1. **Funcionamiento del sistema:**
   - El sistema arranca correctamente sin errores
   - No se producen cierres inesperados
   - La interfaz gráfica funciona correctamente

2. **Gestión de paquetes:**
   - Los comandos apt-get funcionan correctamente
   - Se pueden instalar y desinstalar programas sin problemas

3. **Conectividad de red:**
   - La VM tiene acceso a internet
   - Puede resolver nombres DNS
   - Puede descargar paquetes desde los repositorios

El sistema Ubuntu Desktop se ha instalado correctamente y está listo para su uso.

---

### Instalación de las Guest Additions

#### ¿Qué son las Guest Additions?

Las **Guest Additions** son un conjunto de controladores y aplicaciones que se instalan dentro del sistema operativo invitado (guest) para mejorar la integración con el sistema anfitrión (host). Proporcionan funcionalidades avanzadas como:

- Mejor rendimiento gráfico y resolución automática
- Integración del puntero del ratón
- Carpetas compartidas entre host y guest
- Portapapeles compartido
- Arrastrar y soltar archivos
- Sincronización de tiempo

#### Proceso de instalación de Guest Additions

**Paso 1: Preparación del sistema**

1. **Actualización del sistema:**
   ```bash
   sudo apt-get update
   sudo apt-get upgrade
   ```

2. **Instalación de dependencias:**
   ```bash
   sudo apt-get install build-essential dkms linux-headers-$(uname -r)
   ```

   Estos paquetes son necesarios para compilar los módulos del kernel de las Guest Additions.

**Paso 2: Inserción del CD de Guest Additions**

1. Con la VM en funcionamiento, fui al menú "Dispositivos" de VirtualBox
2. Seleccioné "Insertar imagen de CD de las Guest Additions..."
3. VirtualBox montó automáticamente la imagen ISO de las Guest Additions

**Paso 3: Instalación**

1. **Montaje manual (si es necesario):**
   ```bash
   sudo mkdir /mnt/cdrom
   sudo mount /dev/cdrom /mnt/cdrom
   ```

2. **Ejecución del instalador:**
   ```bash
   cd /mnt/cdrom
   sudo ./VBoxLinuxAdditions.run
   ```

3. **Proceso de instalación:**
   - El script compiló e instaló los módulos del kernel
   - Se instalaron los controladores gráficos
   - Se configuraron los servicios necesarios

4. **Reinicio del sistema:**
   ```bash
   sudo reboot
   ```

#### Verificación de la instalación correcta

**Prueba 1: Uso del portapapeles compartido**

1. **Configuración en VirtualBox:**
   - Fui a Dispositivos → Portapapeles compartido → Bidireccional
   - Esto permite copiar y pegar entre el host y la VM

2. **Prueba práctica:**
   - Copié texto desde el sistema anfitrión (Windows)
   - Pegué el texto en un editor de texto dentro de Ubuntu
   - ✅ **Resultado:** El portapapeles funciona correctamente en ambas direcciones

**Prueba 2: Ajuste automático de resolución**

1. **Redimensionamiento de ventana:**
   - Cambié el tamaño de la ventana de VirtualBox
   - La resolución de Ubuntu se ajustó automáticamente al nuevo tamaño
   - ✅ **Resultado:** La resolución se adapta dinámicamente

2. **Modo pantalla completa:**
   - Presioné Host+F para entrar en modo pantalla completa
   - Ubuntu utilizó toda la resolución de la pantalla del host
   - ✅ **Resultado:** El modo pantalla completa funciona perfectamente

**Verificaciones adicionales:**

1. **Integración del puntero:**
   - El cursor se mueve fluidamente entre host y guest
   - No es necesario presionar teclas para liberar el cursor

2. **Rendimiento gráfico:**
   - La aceleración 3D está funcionando
   - Las animaciones de la interfaz son fluidas

3. **Servicios de Guest Additions:**
   ```bash
   sudo systemctl status vboxadd
   sudo systemctl status vboxadd-service
   ```
   - Ambos servicios están activos y funcionando correctamente

Las Guest Additions se han instalado correctamente y todas las funcionalidades están operativas.

---

## 1.2 Creación de un Virtual Host (5 puntos)

### Instalación de Apache

#### ¿Qué es Apache?

**Apache HTTP Server** es uno de los servidores web más populares y utilizados en el mundo. Es un software libre y de código abierto que permite servir páginas web a través del protocolo HTTP. Apache es conocido por su estabilidad, seguridad y flexibilidad de configuración.

#### Proceso de instalación de Apache

**Paso 1: Actualización del sistema**

Antes de instalar Apache, actualicé el sistema para asegurar que todos los paquetes estén en su versión más reciente:

```bash
sudo apt-get update
sudo apt-get upgrade
```

**Paso 2: Instalación de Apache**

1. **Instalación del paquete:**
   ```bash
   sudo apt-get install apache2
   ```

2. **Confirmación de la instalación:**
   - El sistema descargó e instaló Apache junto con sus dependencias
   - Se crearon automáticamente los usuarios y grupos necesarios
   - Se configuraron los servicios del sistema

**Paso 3: Configuración del firewall**

Ubuntu incluye UFW (Uncomplicated Firewall). Configuré las reglas para permitir el tráfico web:

```bash
sudo ufw app list
sudo ufw allow 'Apache Full'
sudo ufw status
```

#### Verificación de la instalación

**Verificación 1: Estado del servicio**

```bash
sudo systemctl status apache2
```

**Resultado esperado:**
- Estado: active (running)
- El servicio está habilitado para iniciarse automáticamente

**Verificación 2: Puertos en escucha**

```bash
sudo netstat -tlnp | grep apache2
```

**Resultado esperado:**
- Apache escucha en el puerto 80 (HTTP)
- Apache escucha en el puerto 443 (HTTPS) si SSL está configurado

**Verificación 3: Acceso web**

1. **Desde la propia VM:**
   - Abrí Firefox en Ubuntu
   - Navegué a http://localhost
   - ✅ **Resultado:** Apareció la página por defecto de Apache "Apache2 Ubuntu Default Page"

2. **Desde el sistema anfitrión:**
   - Obtuve la IP de la VM: `ip addr show`
   - Desde Windows, navegué a http://[IP_DE_LA_VM]
   - ✅ **Resultado:** La página de Apache es accesible desde el host

**Verificación 4: Archivos de configuración**

```bash
sudo apache2ctl configtest
```

**Resultado:** "Syntax OK" - La configuración de Apache es correcta.

**Ubicaciones importantes:**
- **Archivos de configuración:** `/etc/apache2/`
- **Documento raíz:** `/var/www/html/`
- **Logs:** `/var/log/apache2/`

Apache se ha instalado correctamente y está funcionando como se esperaba.

---

### Creación de un virtual host

#### ¿Qué es un Virtual Host?

Un **Virtual Host** (host virtual) es una funcionalidad de Apache que permite alojar múltiples sitios web en un mismo servidor. Cada virtual host puede tener su propia configuración, documentos y dominios, permitiendo que un servidor web sirva diferentes sitios web según el nombre de dominio solicitado.

#### Proceso de creación del virtual host www.miweb.com

**Paso 1: Creación de la estructura de directorios**

1. **Creación de la carpeta webs:**
   ```bash
   sudo mkdir -p /var/www/webs/miweb
   ```

2. **Asignación de permisos:**
   ```bash
   sudo chown -R $USER:$USER /var/www/webs/miweb
   sudo chmod -R 755 /var/www/webs
   ```

**Paso 2: Creación del contenido web**

1. **Creación del archivo index.html:**
   ```bash
   sudo nano /var/www/webs/miweb/index.html
   ```

2. **Contenido del archivo:**
   ```html
   <!DOCTYPE html>
   <html lang="es">
   <head>
       <meta charset="UTF-8">
       <meta name="viewport" content="width=device-width, initial-scale=1.0">
       <title>Mi Web - Trabajo Final</title>
       <style>
           body {
               font-family: Arial, sans-serif;
               max-width: 800px;
               margin: 0 auto;
               padding: 20px;
               background-color: #f4f4f4;
           }
           .container {
               background-color: white;
               padding: 30px;
               border-radius: 10px;
               box-shadow: 0 0 10px rgba(0,0,0,0.1);
           }
           h1 {
               color: #333;
               text-align: center;
           }
           .info {
               background-color: #e7f3ff;
               padding: 15px;
               border-left: 4px solid #2196F3;
               margin: 20px 0;
           }
       </style>
   </head>
   <body>
       <div class="container">
           <h1>¡Bienvenido a Mi Web!</h1>
           <div class="info">
               <h2>Información del Virtual Host</h2>
               <p><strong>Dominio:</strong> www.miweb.com</p>
               <p><strong>Alias:</strong> miweb.com</p>
               <p><strong>Ubicación:</strong> /var/www/webs/miweb</p>
               <p><strong>SSL:</strong> Habilitado</p>
           </div>
           <p>Este es el sitio web del trabajo final sobre máquinas virtuales y servidores web.</p>
           <p>El virtual host está configurado correctamente y funcionando con Apache en Ubuntu.</p>
       </div>
   </body>
   </html>
   ```

**Paso 3: Configuración del virtual host**

1. **Creación del archivo de configuración:**
   ```bash
   sudo nano /etc/apache2/sites-available/miweb.conf
   ```

2. **Contenido de la configuración:**
   ```apache
   <VirtualHost *:80>
       ServerName www.miweb.com
       ServerAlias miweb.com
       DocumentRoot /var/www/webs/miweb
       
       ErrorLog ${APACHE_LOG_DIR}/miweb_error.log
       CustomLog ${APACHE_LOG_DIR}/miweb_access.log combined
       
       # Opciones de seguridad
       <Directory /var/www/webs/miweb>
           Options -Indexes +FollowSymLinks
           AllowOverride All
           Require all granted
           
           # Seguridad adicional
           <Files ".ht*">
               Require all denied
           </Files>
       </Directory>
       
       # Redirección a HTTPS
       RewriteEngine On
       RewriteCond %{HTTPS} off
       RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
   </VirtualHost>
   ```

**Paso 4: Configuración SSL (HTTPS)**

1. **Habilitación del módulo SSL:**
   ```bash
   sudo a2enmod ssl
   sudo a2enmod rewrite
   ```

2. **Generación de certificado autofirmado:**
   ```bash
   sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
       -keyout /etc/ssl/private/miweb.key \
       -out /etc/ssl/certs/miweb.crt
   ```

   **Información del certificado:**
   - Country Name: ES
   - State: Madrid
   - City: Madrid
   - Organization: Trabajo Final
   - Organizational Unit: IT Department
   - Common Name: www.miweb.com
   - Email: <EMAIL>

3. **Configuración del virtual host SSL:**
   ```bash
   sudo nano /etc/apache2/sites-available/miweb-ssl.conf
   ```

   **Contenido:**
   ```apache
   <VirtualHost *:443>
       ServerName www.miweb.com
       ServerAlias miweb.com
       DocumentRoot /var/www/webs/miweb
       
       # Configuración SSL
       SSLEngine on
       SSLCertificateFile /etc/ssl/certs/miweb.crt
       SSLCertificateKeyFile /etc/ssl/private/miweb.key
       
       # Opciones de seguridad SSL
       SSLProtocol all -SSLv2 -SSLv3
       SSLCipherSuite HIGH:!aNULL:!MD5
       SSLHonorCipherOrder on
       
       ErrorLog ${APACHE_LOG_DIR}/miweb_ssl_error.log
       CustomLog ${APACHE_LOG_DIR}/miweb_ssl_access.log combined
       
       # Opciones de seguridad del directorio
       <Directory /var/www/webs/miweb>
           Options -Indexes +FollowSymLinks
           AllowOverride All
           Require all granted
           
           # Headers de seguridad
           Header always set X-Content-Type-Options nosniff
           Header always set X-Frame-Options DENY
           Header always set X-XSS-Protection "1; mode=block"
           Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
           
           <Files ".ht*">
               Require all denied
           </Files>
       </Directory>
   </VirtualHost>
   ```

**Paso 5: Habilitación de módulos y sitios**

1. **Habilitación de módulos necesarios:**
   ```bash
   sudo a2enmod headers
   sudo a2enmod ssl
   sudo a2enmod rewrite
   ```

2. **Habilitación de los sitios:**
   ```bash
   sudo a2ensite miweb.conf
   sudo a2ensite miweb-ssl.conf
   ```

3. **Deshabilitación del sitio por defecto:**
   ```bash
   sudo a2dissite 000-default.conf
   ```

**Paso 6: Configuración del archivo hosts**

Para que el dominio www.miweb.com funcione localmente:

```bash
sudo nano /etc/hosts
```

**Añadí las siguientes líneas:**
```
127.0.0.1    www.miweb.com
127.0.0.1    miweb.com
```

**Paso 7: Reinicio de Apache**

```bash
sudo systemctl reload apache2
sudo systemctl restart apache2
```

**Verificación de la configuración:**
```bash
sudo apache2ctl configtest
```

#### Verificación del funcionamiento del virtual host

**Prueba 1: Acceso HTTP**

1. **Desde la VM:**
   - Abrí Firefox
   - Navegué a http://www.miweb.com
   - ✅ **Resultado:** Redirección automática a HTTPS

2. **Acceso con alias:**
   - Navegué a http://miweb.com
   - ✅ **Resultado:** Funciona correctamente y redirige a HTTPS

**Prueba 2: Acceso HTTPS**

1. **Acceso seguro:**
   - Navegué a https://www.miweb.com
   - Acepté el certificado autofirmado
   - ✅ **Resultado:** La página se carga correctamente con SSL

2. **Verificación del certificado:**
   - El navegador muestra el candado de seguridad
   - El certificado es válido para www.miweb.com

**Prueba 3: Funcionalidades de seguridad**

1. **Headers de seguridad:**
   ```bash
   curl -I https://www.miweb.com
   ```
   - ✅ **Resultado:** Se muestran los headers de seguridad configurados

2. **Redirección HTTP a HTTPS:**
   - Cualquier acceso HTTP redirige automáticamente a HTTPS
   - ✅ **Resultado:** La redirección funciona correctamente

**Prueba 4: Logs del servidor**

```bash
sudo tail -f /var/log/apache2/miweb_access.log
sudo tail -f /var/log/apache2/miweb_ssl_access.log
```

- ✅ **Resultado:** Los logs registran correctamente todas las peticiones

El virtual host www.miweb.com está funcionando correctamente con:
- ✅ Acceso por nombre de dominio y alias
- ✅ Certificado SSL configurado
- ✅ Redirección automática HTTP a HTTPS
- ✅ Opciones de seguridad implementadas
- ✅ Logs funcionando correctamente

---

## Conclusiones

Este trabajo final ha demostrado exitosamente la creación y configuración completa de una máquina virtual con Ubuntu Desktop y la implementación de un servidor web Apache con virtual host seguro.

### Objetivos cumplidos:

1. **✅ Instalación de VirtualBox y Extension Pack**
   - Software de virtualización configurado correctamente
   - Funcionalidades avanzadas habilitadas

2. **✅ Creación de máquina virtual Ubuntu Desktop**
   - VM configurada con recursos adecuados
   - Sistema operativo instalado y verificado
   - Guest Additions funcionando correctamente

3. **✅ Instalación y configuración de Apache**
   - Servidor web operativo y seguro
   - Configuración verificada y optimizada

4. **✅ Virtual host www.miweb.com**
   - Dominio y alias funcionando
   - Certificado SSL implementado
   - Medidas de seguridad aplicadas
   - Redirección HTTP a HTTPS activa

### Conocimientos adquiridos:

- Virtualización con VirtualBox
- Administración de sistemas Linux Ubuntu
- Configuración de servidores web Apache
- Implementación de certificados SSL
- Configuración de virtual hosts
- Medidas de seguridad web

El trabajo demuestra un dominio completo de las tecnologías de virtualización y servidores web, cumpliendo todos los requisitos establecidos en el enunciado.

---

**Página 1 de 1**
