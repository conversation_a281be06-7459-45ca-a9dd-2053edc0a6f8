#!/bin/bash

# Script de verificación de la instalación completa
# Trabajo Final - Máquinas Virtuales y Servidores Web

echo "=== VERIFICACIÓN DE LA INSTALACIÓN COMPLETA ==="
echo "Este script verificará que todos los componentes estén funcionando correctamente"
echo

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para mostrar resultados
mostrar_resultado() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Función para mostrar información
mostrar_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Función para mostrar advertencias
mostrar_advertencia() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo "1. VERIFICACIÓN DEL SISTEMA OPERATIVO"
echo "======================================"

# Verificar Ubuntu
if [ -f /etc/os-release ]; then
    . /etc/os-release
    mostrar_info "Sistema operativo: $NAME $VERSION"
    if [[ "$NAME" == *"Ubuntu"* ]]; then
        mostrar_resultado 0 "Ubuntu detectado correctamente"
    else
        mostrar_resultado 1 "Sistema operativo no es Ubuntu"
    fi
else
    mostrar_resultado 1 "No se pudo detectar el sistema operativo"
fi

# Verificar arquitectura
ARCH=$(uname -m)
mostrar_info "Arquitectura: $ARCH"

echo
echo "2. VERIFICACIÓN DE VIRTUALBOX (desde host)"
echo "=========================================="

# Nota: Esta verificación debe ejecutarse desde el sistema host
mostrar_advertencia "Esta verificación debe ejecutarse desde el sistema anfitrión (host)"
mostrar_info "Para verificar VirtualBox desde Windows/Mac:"
mostrar_info "  - Abrir VirtualBox"
mostrar_info "  - Verificar que la VM Ubuntu esté listada"
mostrar_info "  - Comprobar que Extension Pack esté instalado"

echo
echo "3. VERIFICACIÓN DE GUEST ADDITIONS"
echo "=================================="

# Verificar módulos de VirtualBox
if lsmod | grep -q vboxguest; then
    mostrar_resultado 0 "Módulo vboxguest cargado"
else
    mostrar_resultado 1 "Módulo vboxguest no encontrado"
fi

# Verificar servicios de VirtualBox
if systemctl is-active --quiet vboxadd; then
    mostrar_resultado 0 "Servicio vboxadd activo"
else
    mostrar_resultado 1 "Servicio vboxadd no activo"
fi

if systemctl is-active --quiet vboxadd-service; then
    mostrar_resultado 0 "Servicio vboxadd-service activo"
else
    mostrar_resultado 1 "Servicio vboxadd-service no activo"
fi

# Verificar resolución automática
RESOLUTION=$(xrandr | grep '*' | awk '{print $1}' | head -1)
mostrar_info "Resolución actual: $RESOLUTION"

echo
echo "4. VERIFICACIÓN DE APACHE"
echo "========================"

# Verificar si Apache está instalado
if command -v apache2 &> /dev/null; then
    mostrar_resultado 0 "Apache2 instalado"
    APACHE_VERSION=$(apache2 -v | head -1)
    mostrar_info "$APACHE_VERSION"
else
    mostrar_resultado 1 "Apache2 no instalado"
    exit 1
fi

# Verificar estado del servicio
if systemctl is-active --quiet apache2; then
    mostrar_resultado 0 "Servicio Apache activo"
else
    mostrar_resultado 1 "Servicio Apache no activo"
fi

# Verificar si está habilitado para inicio automático
if systemctl is-enabled --quiet apache2; then
    mostrar_resultado 0 "Apache habilitado para inicio automático"
else
    mostrar_resultado 1 "Apache no habilitado para inicio automático"
fi

# Verificar puertos en escucha
if netstat -tlnp 2>/dev/null | grep -q ':80.*apache2'; then
    mostrar_resultado 0 "Apache escuchando en puerto 80"
else
    mostrar_resultado 1 "Apache no escucha en puerto 80"
fi

if netstat -tlnp 2>/dev/null | grep -q ':443.*apache2'; then
    mostrar_resultado 0 "Apache escuchando en puerto 443 (SSL)"
else
    mostrar_resultado 1 "Apache no escucha en puerto 443"
fi

echo
echo "5. VERIFICACIÓN DE MÓDULOS APACHE"
echo "================================"

# Verificar módulos críticos
MODULOS=("ssl" "rewrite" "headers")
for modulo in "${MODULOS[@]}"; do
    if apache2ctl -M 2>/dev/null | grep -q "${modulo}_module"; then
        mostrar_resultado 0 "Módulo $modulo habilitado"
    else
        mostrar_resultado 1 "Módulo $modulo no habilitado"
    fi
done

echo
echo "6. VERIFICACIÓN DEL VIRTUAL HOST"
echo "==============================="

# Verificar archivos de configuración
if [ -f /etc/apache2/sites-available/miweb.conf ]; then
    mostrar_resultado 0 "Archivo de configuración HTTP existe"
else
    mostrar_resultado 1 "Archivo de configuración HTTP no existe"
fi

if [ -f /etc/apache2/sites-available/miweb-ssl.conf ]; then
    mostrar_resultado 0 "Archivo de configuración HTTPS existe"
else
    mostrar_resultado 1 "Archivo de configuración HTTPS no existe"
fi

# Verificar sitios habilitados
if [ -L /etc/apache2/sites-enabled/miweb.conf ]; then
    mostrar_resultado 0 "Sitio HTTP habilitado"
else
    mostrar_resultado 1 "Sitio HTTP no habilitado"
fi

if [ -L /etc/apache2/sites-enabled/miweb-ssl.conf ]; then
    mostrar_resultado 0 "Sitio HTTPS habilitado"
else
    mostrar_resultado 1 "Sitio HTTPS no habilitado"
fi

# Verificar directorio web
if [ -d /var/www/webs/miweb ]; then
    mostrar_resultado 0 "Directorio web existe"
    if [ -f /var/www/webs/miweb/index.html ]; then
        mostrar_resultado 0 "Archivo index.html existe"
    else
        mostrar_resultado 1 "Archivo index.html no existe"
    fi
else
    mostrar_resultado 1 "Directorio web no existe"
fi

echo
echo "7. VERIFICACIÓN DE CERTIFICADOS SSL"
echo "==================================="

# Verificar certificados
if [ -f /etc/ssl/certs/miweb.crt ]; then
    mostrar_resultado 0 "Certificado SSL existe"
    
    # Verificar validez del certificado
    if openssl x509 -in /etc/ssl/certs/miweb.crt -noout -checkend 86400 &>/dev/null; then
        mostrar_resultado 0 "Certificado SSL válido"
    else
        mostrar_resultado 1 "Certificado SSL expirado o inválido"
    fi
    
    # Mostrar información del certificado
    CERT_SUBJECT=$(openssl x509 -in /etc/ssl/certs/miweb.crt -noout -subject | cut -d= -f2-)
    CERT_EXPIRES=$(openssl x509 -in /etc/ssl/certs/miweb.crt -noout -enddate | cut -d= -f2)
    mostrar_info "Certificado para: $CERT_SUBJECT"
    mostrar_info "Expira: $CERT_EXPIRES"
else
    mostrar_resultado 1 "Certificado SSL no existe"
fi

if [ -f /etc/ssl/private/miweb.key ]; then
    mostrar_resultado 0 "Clave privada SSL existe"
else
    mostrar_resultado 1 "Clave privada SSL no existe"
fi

echo
echo "8. VERIFICACIÓN DE CONFIGURACIÓN DE HOSTS"
echo "========================================="

# Verificar archivo hosts
if grep -q "www.miweb.com" /etc/hosts; then
    mostrar_resultado 0 "Entrada www.miweb.com en /etc/hosts"
else
    mostrar_resultado 1 "Entrada www.miweb.com no encontrada en /etc/hosts"
fi

if grep -q "miweb.com" /etc/hosts; then
    mostrar_resultado 0 "Entrada miweb.com en /etc/hosts"
else
    mostrar_resultado 1 "Entrada miweb.com no encontrada en /etc/hosts"
fi

echo
echo "9. PRUEBAS DE CONECTIVIDAD"
echo "========================="

# Verificar conectividad HTTP (debe redirigir)
mostrar_info "Probando conectividad HTTP..."
HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost 2>/dev/null)
if [ "$HTTP_RESPONSE" = "301" ] || [ "$HTTP_RESPONSE" = "302" ]; then
    mostrar_resultado 0 "Redirección HTTP funcionando (código: $HTTP_RESPONSE)"
elif [ "$HTTP_RESPONSE" = "200" ]; then
    mostrar_advertencia "HTTP responde 200 (debería redirigir a HTTPS)"
else
    mostrar_resultado 1 "HTTP no responde correctamente (código: $HTTP_RESPONSE)"
fi

# Verificar conectividad HTTPS
mostrar_info "Probando conectividad HTTPS..."
if curl -k -s https://localhost >/dev/null 2>&1; then
    mostrar_resultado 0 "HTTPS responde correctamente"
else
    mostrar_resultado 1 "HTTPS no responde"
fi

# Verificar virtual host específico
mostrar_info "Probando virtual host www.miweb.com..."
if curl -k -s -H "Host: www.miweb.com" https://localhost >/dev/null 2>&1; then
    mostrar_resultado 0 "Virtual host www.miweb.com responde"
else
    mostrar_resultado 1 "Virtual host www.miweb.com no responde"
fi

echo
echo "10. VERIFICACIÓN DE LOGS"
echo "======================="

# Verificar archivos de log
LOGS=("/var/log/apache2/miweb_error.log" "/var/log/apache2/miweb_access.log" "/var/log/apache2/miweb_ssl_error.log" "/var/log/apache2/miweb_ssl_access.log")

for log in "${LOGS[@]}"; do
    if [ -f "$log" ]; then
        mostrar_resultado 0 "Log $(basename $log) existe"
    else
        mostrar_resultado 1 "Log $(basename $log) no existe"
    fi
done

echo
echo "11. VERIFICACIÓN DE CONFIGURACIÓN APACHE"
echo "========================================"

# Verificar sintaxis de configuración
if apache2ctl configtest &>/dev/null; then
    mostrar_resultado 0 "Configuración de Apache válida"
else
    mostrar_resultado 1 "Errores en configuración de Apache"
    mostrar_info "Ejecutar 'sudo apache2ctl configtest' para ver detalles"
fi

echo
echo "12. RESUMEN DE VERIFICACIÓN"
echo "=========================="

# Realizar prueba final completa
mostrar_info "Realizando prueba final..."

# Contador de errores
ERRORES=0

# Verificaciones críticas
systemctl is-active --quiet apache2 || ((ERRORES++))
[ -f /etc/apache2/sites-enabled/miweb.conf ] || ((ERRORES++))
[ -f /etc/apache2/sites-enabled/miweb-ssl.conf ] || ((ERRORES++))
[ -f /var/www/webs/miweb/index.html ] || ((ERRORES++))
[ -f /etc/ssl/certs/miweb.crt ] || ((ERRORES++))
apache2ctl configtest &>/dev/null || ((ERRORES++))

if [ $ERRORES -eq 0 ]; then
    echo -e "${GREEN}"
    echo "🎉 ¡VERIFICACIÓN COMPLETADA EXITOSAMENTE!"
    echo "========================================="
    echo "✅ Todos los componentes están funcionando correctamente"
    echo "✅ El virtual host www.miweb.com está operativo"
    echo "✅ SSL está configurado y funcionando"
    echo "✅ Apache está ejecutándose sin errores"
    echo -e "${NC}"
    
    echo
    mostrar_info "Puedes acceder al sitio web en:"
    echo "  🌐 https://www.miweb.com"
    echo "  🌐 https://miweb.com"
    echo "  🌐 http://www.miweb.com (redirige a HTTPS)"
    echo "  🌐 http://miweb.com (redirige a HTTPS)"
    
else
    echo -e "${RED}"
    echo "❌ VERIFICACIÓN COMPLETADA CON ERRORES"
    echo "======================================"
    echo "Se encontraron $ERRORES errores críticos"
    echo "Revisa los mensajes anteriores para más detalles"
    echo -e "${NC}"
fi

echo
echo "=== FIN DE LA VERIFICACIÓN ==="
