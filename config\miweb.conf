# Configuración del Virtual Host para www.miweb.com
# Trabajo Final - Máquinas Virtuales y Servidores Web
# Archivo: /etc/apache2/sites-available/miweb.conf

<VirtualHost *:80>
    # Configuración básica del servidor
    ServerName www.miweb.com
    ServerAlias miweb.com
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/webs/miweb
    
    # Configuración de logs
    ErrorLog ${APACHE_LOG_DIR}/miweb_error.log
    CustomLog ${APACHE_LOG_DIR}/miweb_access.log combined
    
    # Configuración del directorio principal
    <Directory /var/www/webs/miweb>
        # Opciones de seguridad
        Options -Indexes +FollowSymLinks -ExecCGI
        AllowOverride All
        Require all granted
        
        # Prevenir acceso a archivos de configuración
        <Files ".ht*">
            Require all denied
        </Files>
        
        # Prevenir acceso a archivos de backup
        <FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
            Require all denied
        </FilesMatch>
        
        # Configuración de tipos MIME
        <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
            ExpiresActive On
            ExpiresDefault "access plus 1 month"
        </FilesMatch>
    </Directory>
    
    # Redirección automática a HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
    
    # Configuración adicional de seguridad
    ServerTokens Prod
    ServerSignature Off
</VirtualHost>

# Configuración adicional para mejorar la seguridad
# Estas directivas se pueden incluir en el archivo principal de configuración

# Ocultar información del servidor
ServerTokens Prod
ServerSignature Off

# Prevenir ataques de clickjacking
Header always append X-Frame-Options SAMEORIGIN

# Prevenir ataques XSS
Header always set X-XSS-Protection "1; mode=block"

# Prevenir MIME type sniffing
Header always set X-Content-Type-Options nosniff

# Configuración de compresión (opcional)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
