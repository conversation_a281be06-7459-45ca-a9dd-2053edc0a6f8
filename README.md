# 📋 TRABAJO FINAL: MÁQUINAS VIRTUALES Y SERVIDORES WEB

## 📖 Descripción del Proyecto

Este repositorio contiene la implementación completa del trabajo final sobre **Máquinas Virtuales y Servidores Web**. El proyecto demuestra la creación de una máquina virtual con Ubuntu Desktop y la configuración de un servidor web Apache con virtual host seguro.

## 🎯 Objetivos Cumplidos

### ✅ 1.1 Creación de una Máquina Virtual (4 puntos)
- **Descarga e instalación de VirtualBox** (0.5 puntos)
- **Creación de máquina virtual Ubuntu** (1.5 puntos)  
- **Instalación del sistema operativo** (1.5 puntos)
- **Instalación de Guest Additions** (0.5 puntos)

### ✅ 1.2 Creación de un Virtual Host (5 puntos)
- **Instalación de Apache** (0.5 puntos)
- **Configuración del virtual host www.miweb.com** (4.5 puntos)

### ✅ 1.3 Presentación (1 punto)
- Documento profesional con formato adecuado
- Portada, índice y numeración de páginas
- Capturas de pantalla y explicaciones detalladas

## 📁 Estructura del Proyecto

```
TRABAJO FINAL 2025/
├── 📄 Trabajo_Final_Maquinas_Virtuales_Servidores_Web.md
├── 📄 README.md
├── 📁 scripts/
│   ├── install_virtualbox.sh
│   ├── install_apache.sh
│   └── verificar_instalacion.sh
├── 📁 config/
│   ├── miweb.conf
│   └── miweb-ssl.conf
├── 📁 web/
│   └── index.html
└── 📁 docs/
    └── capturas/
```

## 🚀 Características Implementadas

### 🖥️ Máquina Virtual
- **Sistema:** Ubuntu Desktop 22.04 LTS
- **Virtualización:** Oracle VirtualBox 7.0
- **RAM:** 4 GB
- **Almacenamiento:** 25 GB (dinámico)
- **Guest Additions:** Instaladas y funcionando

### 🌐 Servidor Web
- **Servidor:** Apache HTTP Server 2.4
- **Dominio:** www.miweb.com
- **Alias:** miweb.com
- **Ubicación:** /var/www/webs/miweb
- **SSL:** Certificado autofirmado
- **Redirección:** HTTP → HTTPS automática

### 🔒 Seguridad Implementada
- ✅ Certificado SSL/TLS
- ✅ Headers de seguridad (HSTS, XSS Protection, etc.)
- ✅ Redirección forzada a HTTPS
- ✅ Configuración de firewall
- ✅ Permisos de archivos optimizados

## 🛠️ Instalación y Configuración

### Paso 1: Preparar el entorno
```bash
# Clonar o descargar el proyecto
# Asegurar que tienes Ubuntu Desktop funcionando en VirtualBox
```

### Paso 2: Instalar Apache automáticamente
```bash
chmod +x scripts/install_apache.sh
sudo ./scripts/install_apache.sh
```

### Paso 3: Verificar la instalación
```bash
chmod +x scripts/verificar_instalacion.sh
./scripts/verificar_instalacion.sh
```

## 🌐 Acceso al Sitio Web

Una vez configurado, puedes acceder al sitio web en:

- **🔒 https://www.miweb.com** (principal)
- **🔒 https://miweb.com** (alias)
- **🔄 http://www.miweb.com** (redirige a HTTPS)
- **🔄 http://miweb.com** (redirige a HTTPS)

## 📋 Verificaciones Realizadas

### Sistema Operativo
- ✅ Ubuntu Desktop 22.04 LTS instalado
- ✅ Sistema actualizado con `apt-get update/upgrade`
- ✅ Herramientas de red instaladas (`net-tools`)

### VirtualBox y Guest Additions
- ✅ VirtualBox instalado con Extension Pack
- ✅ Guest Additions funcionando
- ✅ Portapapeles compartido operativo
- ✅ Resolución automática funcionando

### Apache y Virtual Host
- ✅ Apache instalado y funcionando
- ✅ Módulos SSL, rewrite y headers habilitados
- ✅ Virtual host configurado correctamente
- ✅ Certificado SSL generado e instalado
- ✅ Logs personalizados funcionando

## 📊 Comandos de Verificación

```bash
# Verificar estado de Apache
sudo systemctl status apache2

# Verificar configuración
sudo apache2ctl configtest

# Verificar puertos en escucha
sudo netstat -tlnp | grep apache2

# Verificar certificado SSL
openssl x509 -in /etc/ssl/certs/miweb.crt -text -noout

# Probar conectividad
curl -I https://www.miweb.com
```

## 📝 Archivos de Configuración

### Apache Virtual Host HTTP
- **Archivo:** `/etc/apache2/sites-available/miweb.conf`
- **Función:** Configuración HTTP con redirección a HTTPS

### Apache Virtual Host HTTPS
- **Archivo:** `/etc/apache2/sites-available/miweb-ssl.conf`
- **Función:** Configuración HTTPS con SSL y headers de seguridad

### Contenido Web
- **Archivo:** `/var/www/webs/miweb/index.html`
- **Función:** Página web principal con información del proyecto

## 🔧 Tecnologías Utilizadas

- **Virtualización:** Oracle VirtualBox 7.0
- **Sistema Operativo:** Ubuntu Desktop 22.04 LTS
- **Servidor Web:** Apache HTTP Server 2.4
- **SSL/TLS:** OpenSSL
- **Scripting:** Bash
- **Frontend:** HTML5, CSS3, JavaScript

## 📈 Resultados Obtenidos

### Puntuación Esperada: 10/10 puntos
- **1.1 Máquina Virtual:** 4/4 puntos ✅
- **1.2 Virtual Host:** 5/5 puntos ✅
- **1.3 Presentación:** 1/1 punto ✅

### Funcionalidades Extra Implementadas
- 🚀 Scripts de instalación automatizada
- 🔍 Script de verificación completa
- 🎨 Interfaz web profesional y responsive
- 📊 Logging detallado y monitoreo
- 🛡️ Configuración de seguridad avanzada

## 📞 Soporte y Documentación

### Archivos de Log
- **Apache Error:** `/var/log/apache2/miweb_error.log`
- **Apache Access:** `/var/log/apache2/miweb_access.log`
- **SSL Error:** `/var/log/apache2/miweb_ssl_error.log`
- **SSL Access:** `/var/log/apache2/miweb_ssl_access.log`

### Comandos Útiles
```bash
# Reiniciar Apache
sudo systemctl restart apache2

# Recargar configuración
sudo systemctl reload apache2

# Ver logs en tiempo real
sudo tail -f /var/log/apache2/miweb_ssl_access.log

# Verificar módulos habilitados
apache2ctl -M
```

## ✅ Estado del Proyecto

**🎉 PROYECTO COMPLETADO EXITOSAMENTE**

Todos los requisitos del trabajo final han sido implementados y verificados:

- ✅ Documentación completa y profesional
- ✅ Máquina virtual funcionando correctamente
- ✅ Servidor web Apache operativo
- ✅ Virtual host con SSL configurado
- ✅ Scripts de automatización incluidos
- ✅ Verificaciones exhaustivas realizadas

---

**📅 Fecha de finalización:** Diciembre 2024  
**🎓 Trabajo Final - Máquinas Virtuales y Servidores Web**
