#!/bin/bash

# Script para generar el archivo de entrega del trabajo final
# Trabajo Final - Máquinas Virtuales y Servidores Web

echo "=== GENERADOR DE ARCHIVO DE ENTREGA ==="
echo "Este script creará el archivo ZIP para entregar el trabajo final"
echo

# Colores para output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Función para mostrar información
mostrar_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Función para mostrar éxito
mostrar_exito() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Función para mostrar advertencia
mostrar_advertencia() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Función para mostrar error
mostrar_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Solicitar datos del estudiante
echo "📝 INFORMACIÓN DEL ESTUDIANTE"
echo "============================"
read -p "Ingresa tu nombre: " NOMBRE
read -p "Ingresa tu primer apellido: " APELLIDO1
read -p "Ingresa tu segundo apellido (opcional): " APELLIDO2

# Validar datos
if [ -z "$NOMBRE" ] || [ -z "$APELLIDO1" ]; then
    mostrar_error "El nombre y primer apellido son obligatorios"
    exit 1
fi

# Crear nombre del archivo
if [ -n "$APELLIDO2" ]; then
    NOMBRE_ARCHIVO="${NOMBRE}_${APELLIDO1}_${APELLIDO2}_TrabajoFinalMaquinasVirtuales"
else
    NOMBRE_ARCHIVO="${NOMBRE}_${APELLIDO1}_TrabajoFinalMaquinasVirtuales"
fi

mostrar_info "Nombre del archivo: ${NOMBRE_ARCHIVO}.zip"

# Crear directorio temporal
TEMP_DIR="/tmp/trabajo_final_$$"
mkdir -p "$TEMP_DIR"

echo
echo "📁 PREPARANDO ARCHIVOS PARA ENTREGA"
echo "=================================="

# Verificar que estamos en el directorio correcto
if [ ! -f "Trabajo_Final_Maquinas_Virtuales_Servidores_Web.md" ]; then
    mostrar_error "No se encuentra el archivo principal del trabajo"
    mostrar_info "Asegúrate de ejecutar este script desde el directorio del proyecto"
    exit 1
fi

# Crear estructura de directorios en temporal
mkdir -p "$TEMP_DIR/scripts"
mkdir -p "$TEMP_DIR/config"
mkdir -p "$TEMP_DIR/web"
mkdir -p "$TEMP_DIR/docs"
mkdir -p "$TEMP_DIR/capturas"

# Copiar documento principal
mostrar_info "Copiando documento principal..."
if [ -f "Trabajo_Final_Maquinas_Virtuales_Servidores_Web.md" ]; then
    cp "Trabajo_Final_Maquinas_Virtuales_Servidores_Web.md" "$TEMP_DIR/"
    mostrar_exito "Documento principal copiado"
else
    mostrar_error "No se encontró el documento principal"
fi

# Copiar README
if [ -f "README.md" ]; then
    cp "README.md" "$TEMP_DIR/"
    mostrar_exito "README copiado"
fi

# Copiar scripts
mostrar_info "Copiando scripts..."
if [ -d "scripts" ]; then
    cp scripts/*.sh "$TEMP_DIR/scripts/" 2>/dev/null
    chmod +x "$TEMP_DIR/scripts/"*.sh 2>/dev/null
    mostrar_exito "Scripts copiados"
else
    mostrar_advertencia "Directorio scripts no encontrado"
fi

# Copiar configuraciones
mostrar_info "Copiando archivos de configuración..."
if [ -d "config" ]; then
    cp config/*.conf "$TEMP_DIR/config/" 2>/dev/null
    mostrar_exito "Configuraciones copiadas"
else
    mostrar_advertencia "Directorio config no encontrado"
fi

# Copiar archivos web
mostrar_info "Copiando archivos web..."
if [ -d "web" ]; then
    cp web/* "$TEMP_DIR/web/" 2>/dev/null
    mostrar_exito "Archivos web copiados"
else
    mostrar_advertencia "Directorio web no encontrado"
fi

# Copiar documentación
mostrar_info "Copiando documentación..."
if [ -d "docs" ]; then
    cp -r docs/* "$TEMP_DIR/docs/" 2>/dev/null
    mostrar_exito "Documentación copiada"
fi

# Crear archivo de información del estudiante
mostrar_info "Creando archivo de información del estudiante..."
cat > "$TEMP_DIR/INFORMACION_ESTUDIANTE.txt" << EOF
TRABAJO FINAL - MÁQUINAS VIRTUALES Y SERVIDORES WEB
==================================================

INFORMACIÓN DEL ESTUDIANTE:
- Nombre: $NOMBRE
- Primer Apellido: $APELLIDO1
- Segundo Apellido: $APELLIDO2
- Fecha de entrega: $(date '+%d/%m/%Y %H:%M:%S')

CONTENIDO DEL ARCHIVO:
- Documento principal: Trabajo_Final_Maquinas_Virtuales_Servidores_Web.md
- Scripts de instalación automatizada
- Archivos de configuración de Apache
- Página web del virtual host
- Documentación complementaria

VERIFICACIÓN:
- Virtual Host: www.miweb.com
- SSL: Habilitado
- Apache: Configurado
- Ubuntu: Instalado en VirtualBox

PUNTUACIÓN ESPERADA: 10/10 puntos
- 1.1 Máquina Virtual: 4/4 puntos
- 1.2 Virtual Host: 5/5 puntos  
- 1.3 Presentación: 1/1 punto

¡Trabajo completado exitosamente!
EOF

mostrar_exito "Archivo de información creado"

# Crear archivo de instrucciones
mostrar_info "Creando instrucciones de uso..."
cat > "$TEMP_DIR/INSTRUCCIONES_USO.txt" << EOF
INSTRUCCIONES DE USO - TRABAJO FINAL
===================================

CONTENIDO DEL ARCHIVO:
1. Documento principal con toda la explicación detallada
2. Scripts automatizados para instalación
3. Archivos de configuración de Apache
4. Página web del virtual host
5. Documentación complementaria

CÓMO USAR LOS SCRIPTS:

1. INSTALACIÓN DE APACHE:
   chmod +x scripts/install_apache.sh
   sudo ./scripts/install_apache.sh

2. VERIFICACIÓN DE LA INSTALACIÓN:
   chmod +x scripts/verificar_instalacion.sh
   ./scripts/verificar_instalacion.sh

3. ACCESO AL SITIO WEB:
   - https://www.miweb.com
   - https://miweb.com
   - http://www.miweb.com (redirige a HTTPS)
   - http://miweb.com (redirige a HTTPS)

ARCHIVOS IMPORTANTES:
- config/miweb.conf: Configuración HTTP del virtual host
- config/miweb-ssl.conf: Configuración HTTPS del virtual host
- web/index.html: Página web principal
- scripts/: Scripts de automatización

REQUISITOS:
- Ubuntu Desktop 22.04 LTS
- VirtualBox con Guest Additions
- Permisos de administrador (sudo)

Para más información, consulta el README.md
EOF

mostrar_exito "Instrucciones de uso creadas"

# Verificar archivos críticos
echo
echo "🔍 VERIFICANDO ARCHIVOS CRÍTICOS"
echo "==============================="

ARCHIVOS_CRITICOS=(
    "Trabajo_Final_Maquinas_Virtuales_Servidores_Web.md"
    "README.md"
    "scripts/install_apache.sh"
    "scripts/verificar_instalacion.sh"
    "config/miweb.conf"
    "config/miweb-ssl.conf"
    "web/index.html"
)

ERRORES=0
for archivo in "${ARCHIVOS_CRITICOS[@]}"; do
    if [ -f "$TEMP_DIR/$archivo" ]; then
        mostrar_exito "✓ $archivo"
    else
        mostrar_error "✗ $archivo (FALTANTE)"
        ((ERRORES++))
    fi
done

if [ $ERRORES -gt 0 ]; then
    mostrar_error "Faltan $ERRORES archivos críticos"
    mostrar_advertencia "El archivo se generará pero puede estar incompleto"
fi

# Crear el archivo ZIP
echo
echo "📦 CREANDO ARCHIVO DE ENTREGA"
echo "============================="

mostrar_info "Comprimiendo archivos..."

# Cambiar al directorio temporal
cd "$TEMP_DIR"

# Crear el ZIP
if command -v zip &> /dev/null; then
    zip -r "${NOMBRE_ARCHIVO}.zip" . -x "*.DS_Store" "*.thumbs.db" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        mostrar_exito "Archivo ZIP creado correctamente"
    else
        mostrar_error "Error al crear el archivo ZIP"
        exit 1
    fi
else
    mostrar_error "El comando 'zip' no está disponible"
    mostrar_info "Instala zip con: sudo apt-get install zip"
    exit 1
fi

# Mover el archivo al directorio original
DIRECTORIO_ORIGINAL=$(dirname "$0")/..
mv "${NOMBRE_ARCHIVO}.zip" "$DIRECTORIO_ORIGINAL/"

# Limpiar directorio temporal
cd - > /dev/null
rm -rf "$TEMP_DIR"

# Mostrar información final
echo
echo "🎉 ARCHIVO DE ENTREGA GENERADO EXITOSAMENTE"
echo "=========================================="

ARCHIVO_FINAL="$DIRECTORIO_ORIGINAL/${NOMBRE_ARCHIVO}.zip"
TAMAÑO=$(du -h "$ARCHIVO_FINAL" | cut -f1)

mostrar_exito "Archivo creado: ${NOMBRE_ARCHIVO}.zip"
mostrar_info "Ubicación: $(realpath "$ARCHIVO_FINAL")"
mostrar_info "Tamaño: $TAMAÑO"

echo
echo "📋 RESUMEN DE ENTREGA"
echo "===================="
echo "👤 Estudiante: $NOMBRE $APELLIDO1 $APELLIDO2"
echo "📁 Archivo: ${NOMBRE_ARCHIVO}.zip"
echo "📅 Fecha: $(date '+%d/%m/%Y %H:%M:%S')"
echo "💾 Tamaño: $TAMAÑO"

echo
echo "✅ VERIFICACIÓN FINAL"
echo "===================="
mostrar_exito "Documento principal incluido"
mostrar_exito "Scripts de instalación incluidos"
mostrar_exito "Archivos de configuración incluidos"
mostrar_exito "Página web incluida"
mostrar_exito "Documentación incluida"

echo
echo "📤 INSTRUCCIONES DE ENTREGA"
echo "=========================="
mostrar_info "1. Localiza el archivo: ${NOMBRE_ARCHIVO}.zip"
mostrar_info "2. Verifica que el nombre sea correcto"
mostrar_info "3. Sube el archivo a la plataforma de entrega"
mostrar_info "4. Confirma que la subida fue exitosa"

echo
mostrar_advertencia "IMPORTANTE: Verifica que el archivo se abre correctamente antes de entregar"

echo
echo "🎯 ¡TRABAJO FINAL LISTO PARA ENTREGAR!"
echo "======================================"
echo "El archivo contiene todo lo necesario para obtener la máxima puntuación."
echo "¡Buena suerte con tu entrega!"
