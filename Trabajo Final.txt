



MÁQUINAS VIRTUALES Y SERVIDORES WEB (APACHE)















TRABAJO FINAL

TRABAJO FINAL : MÁQUINAS VIRTUALES Y SERVIDORES WEB

                    TRABAJO FINAL



1.0. INSTRUCCIONES

A continuación, se muestran las instrucciones que debes tener en cuenta para la realización del trabajo final.

- Forma de entrega:
■ Deberás entregar el ejercicio en un archivo comprimido que se llamará: nombre_apellido1_nombreTrabajo.zip o .rar


- El archivo comprimido deberá contener:
■ Un documento de texto (archivo .txt, .docx o .pdf) en el que resolverás de forma clara y detallada cada uno de los apartados expuestos en el enunciado. Debes realizar el trabajo de forma profesional, es decir, cui- dando  tanto  la  presentación  como  las  explicaciones  realizadas.

Recuerda que todas las explicaciones deberán ir acompañadas de cap- turas de pantalla (no fotos) y estas deberán mantener las mismas pro- porciones durante todo el trabajo.


- Recomendaciones:
■ Leer el enunciado completo antes de empezar la realización del ejercicio.
■ Consultar todas las dudas que te hayan surgido al leer el enunciado del ejercicio con los docentes del curso para que puedan ayudarte a resol- verlas, antes de que comiences el desarrollo del trabajo.
■ Recuerda cuidar la presentación de los archivos entregados.


Atención: Si no se envía el ejercicio tal y como se ha solicitado NO se corregirá.






2

TRABAJO FINAL : MÁQUINAS VIRTUALES Y SERVIDORES WEB

1.1. CREACIÓN DE UNA MÁQUINA VIRTUAL (4 PUNTOS)

Descarga e instalación de VirtualBox o UTM (0.5 Puntos)

Importante:
■ Si dispones de un ordenador con sistema operativo de Windows o un Mac con chip de Intel, deberás hacer uso del programa VirtualBox.
■ Si dispones de un Mac con chip ARM, deberás hacer uso del programa UTM.


En este apartado deberás explicar qué programa de virtualización has utilizado y cómo has realizado su descarga e instalación. Tras ello, será necesario que reali- ces un breve recorrido por su interface explicando sus funcionalidades principa- les. Puedes mostrar una captura de pantalla de la interfaz para ayudarte con la explicación.


En caso de realizar la instalación de VirtualBox, será necesario que descargues e instales también su paquete de herramientas, llamado “Extension Pack”, deta- llando el proceso que has seguido para ello y explicando el motivo por el que es conveniente realizar su instalación.




Creación de una máquina virtual (1.5 puntos)

A continuación, deberás explicar brevemente que es una máquina virtual y qué herramientas y programas necesitas para crear una. Tras ello, deberás crear una máquina virtual para la distribución “Ubuntu Desktop” del sistema operativo GNU/Linux

Recuerda que deberás explicar detalladamente el proceso de creación de la má- quina virtual, detallando todo lo que necesitas tener en cuenta para crearla de forma correcta y adjuntando capturas de pantalla (no fotos) que complementen tu explicación.







3

TRABAJO FINAL : MÁQUINAS VIRTUALES Y SERVIDORES WEB


Instalación del sistema operativo (1.5 Puntos)

En este apartado, deberás explicar detalladamente cómo has realizado la instala- ción del sistema operativo Ubuntu Desktop, especificando las elecciones que has tomado durante el proceso y adjuntando capturas de pantalla que complemen- ten tu explicación.

Tras la instalación, deberás comprobar que el sistema operativo se ha instalado correctamente. Que funcione correctamente significa que no se apague sola, que no de errores durante su inicio y cierre, que no surjan errores durante la instala- ción de otros programas, …

Puedes ayudarte de los siguientes comandos para realizar la comprobación:

sudo apt-get update sudo apt-get upgrade
sudo apt-get install net-tools





Instalación de las guest additions (0.5 Puntos)

Importante: Todos aquellos que utilicéis un ordenador Mac con chip ARM estáis exentos de realizar este apartado, ya que estas utilidades se incluyen durante la instalación de UTM.

Este apartado será obligatorio para quienes estéis utilizando el programa Virtual- Box.

A continuación, deberás explicar detalladamente como realizas el proceso de ins- talación de las “guest additions” en la máquina virtual de Ubuntu que has creado.

Tras ello, deberás comprobar que se han instalado correctamente, mostrando:
■ Uso del portapapeles entre la máquina virtual y el sistema anfitrión (host)
■ Como se ha ajustado automáticamente la resolución de la máquina vir- tual a la pantalla del host.






4

TRABAJO FINAL : MÁQUINAS VIRTUALES Y SERVIDORES WEB

1.2. CREACIÓN DE UN VIRTUAL HOST (5 PUNTOS)

Instalación de Apache (0.5 Puntos)

En este apartado, deberás detallar como has instalado apache en la máquina vir- tual de Ubuntu que has creado, adjuntando capturas de pantalla del proceso.

Recuerda que deberás comprobar que el programa se haya instalado correcta- mente.




Creación de un virtual host (4.5 Puntos)

Por último, será necesario que detalles el proceso que has seguido para crear un virtual host llamado: www.miweb.com , con certificado SSL y opciones de seguri- dad, adjuntando capturas de pantalla del proceso.

Para que este apartado se pueda valorar, deberás mostrar el funcionamiento del virtual host.

Indicaciones a tener en cuenta:
■ El contenido del virtual host deberás crearlo en una carpeta llamada webs, que se situará en la siguiente ruta:

/var/www




■ El virtual host deberá ser accesible haciendo uso de su nombre: www.miweb.com y de su alias: miweb.com













5

TRABAJO FINAL : MÁQUINAS VIRTUALES Y SERVIDORES WEB

1.3. PRESENTACIÓN (1 PUNTO)
Atención: Este apartado sólo se valorará si se han realizado los anteriores. De forma obligatoria el trabajo deberá contener:
■ Una portada con el nombre del ejercicio


■ Tus datos (nombre y apellidos). Por ejemplo, si creas un documento de Word/PDF podrás añadirlos en el encabezado, si creas una presentación podrás insertarlos a la plantilla como marca de agua.


■ Un índice o tabla de contenido


■ Páginas numeradas


Además, será necesario que:


■ Tengas cuidado con las faltas de ortografía y no abuses de las mayúscu- las


■ Compruebes que todas las imágenes mantengan el mismo tamaño y for- mato


■ Utilices una fuente y tamaño de letra que permita que tu trabajo sea le- gible.


■ Uses un máximo de 2-3 colores (en caso de que decidas utilizarlos).












6
