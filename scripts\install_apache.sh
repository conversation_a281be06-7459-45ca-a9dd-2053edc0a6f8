#!/bin/bash

# Script para instalación y configuración de Apache
# Trabajo Final - Máquinas Virtuales y Servidores Web

echo "=== INSTALACIÓN Y CONFIGURACIÓN DE APACHE ==="
echo "Este script instalará Apache y configurará el virtual host www.miweb.com"
echo

# Actualizar el sistema
echo "1. Actualizando el sistema..."
sudo apt-get update
sudo apt-get upgrade -y

# Instalar Apache
echo "2. Instalando Apache..."
sudo apt-get install -y apache2

# Instalar herramientas adicionales
echo "3. Instalando herramientas adicionales..."
sudo apt-get install -y openssl net-tools curl

# Habilitar módulos necesarios
echo "4. Habilitando módulos de Apache..."
sudo a2enmod ssl
sudo a2enmod rewrite
sudo a2enmod headers

# Configurar firewall
echo "5. Configurando firewall..."
sudo ufw allow 'Apache Full'

# Crear estructura de directorios
echo "6. Creando estructura de directorios..."
sudo mkdir -p /var/www/webs/miweb

# Crear contenido web
echo "7. Creando contenido web..."
sudo tee /var/www/webs/miweb/index.html > /dev/null <<EOF
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Web - Trabajo Final</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
        .status {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>¡Bienvenido a Mi Web!</h1>
        <div class="status">
            ✅ Virtual Host configurado correctamente
        </div>
        <div class="info">
            <h2>Información del Virtual Host</h2>
            <p><strong>Dominio:</strong> www.miweb.com</p>
            <p><strong>Alias:</strong> miweb.com</p>
            <p><strong>Ubicación:</strong> /var/www/webs/miweb</p>
            <p><strong>SSL:</strong> Habilitado</p>
            <p><strong>Fecha de instalación:</strong> $(date)</p>
        </div>
        <p>Este es el sitio web del trabajo final sobre máquinas virtuales y servidores web.</p>
        <p>El virtual host está configurado correctamente y funcionando con Apache en Ubuntu.</p>
        
        <h3>Características implementadas:</h3>
        <ul>
            <li>✅ Certificado SSL autofirmado</li>
            <li>✅ Redirección automática HTTP → HTTPS</li>
            <li>✅ Headers de seguridad configurados</li>
            <li>✅ Logs personalizados</li>
            <li>✅ Alias de dominio funcional</li>
        </ul>
    </div>
</body>
</html>
EOF

# Asignar permisos
echo "8. Asignando permisos..."
sudo chown -R www-data:www-data /var/www/webs/miweb
sudo chmod -R 755 /var/www/webs

# Generar certificado SSL
echo "9. Generando certificado SSL..."
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/miweb.key \
    -out /etc/ssl/certs/miweb.crt \
    -subj "/C=ES/ST=Madrid/L=Madrid/O=Trabajo Final/OU=IT Department/CN=www.miweb.com/emailAddress=<EMAIL>"

# Crear configuración del virtual host HTTP
echo "10. Creando configuración del virtual host HTTP..."
sudo tee /etc/apache2/sites-available/miweb.conf > /dev/null <<EOF
<VirtualHost *:80>
    ServerName www.miweb.com
    ServerAlias miweb.com
    DocumentRoot /var/www/webs/miweb
    
    ErrorLog \${APACHE_LOG_DIR}/miweb_error.log
    CustomLog \${APACHE_LOG_DIR}/miweb_access.log combined
    
    # Opciones de seguridad
    <Directory /var/www/webs/miweb>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Seguridad adicional
        <Files ".ht*">
            Require all denied
        </Files>
    </Directory>
    
    # Redirección a HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
</VirtualHost>
EOF

# Crear configuración del virtual host HTTPS
echo "11. Creando configuración del virtual host HTTPS..."
sudo tee /etc/apache2/sites-available/miweb-ssl.conf > /dev/null <<EOF
<VirtualHost *:443>
    ServerName www.miweb.com
    ServerAlias miweb.com
    DocumentRoot /var/www/webs/miweb
    
    # Configuración SSL
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/miweb.crt
    SSLCertificateKeyFile /etc/ssl/private/miweb.key
    
    # Opciones de seguridad SSL
    SSLProtocol all -SSLv2 -SSLv3
    SSLCipherSuite HIGH:!aNULL:!MD5
    SSLHonorCipherOrder on
    
    ErrorLog \${APACHE_LOG_DIR}/miweb_ssl_error.log
    CustomLog \${APACHE_LOG_DIR}/miweb_ssl_access.log combined
    
    # Opciones de seguridad del directorio
    <Directory /var/www/webs/miweb>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Headers de seguridad
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
        
        <Files ".ht*">
            Require all denied
        </Files>
    </Directory>
</VirtualHost>
EOF

# Configurar archivo hosts
echo "12. Configurando archivo hosts..."
if ! grep -q "www.miweb.com" /etc/hosts; then
    echo "127.0.0.1    www.miweb.com" | sudo tee -a /etc/hosts
    echo "127.0.0.1    miweb.com" | sudo tee -a /etc/hosts
fi

# Habilitar sitios
echo "13. Habilitando sitios..."
sudo a2ensite miweb.conf
sudo a2ensite miweb-ssl.conf
sudo a2dissite 000-default.conf

# Verificar configuración
echo "14. Verificando configuración..."
sudo apache2ctl configtest

# Reiniciar Apache
echo "15. Reiniciando Apache..."
sudo systemctl restart apache2
sudo systemctl enable apache2

echo
echo "=== CONFIGURACIÓN COMPLETADA ==="
echo "Apache y el virtual host www.miweb.com han sido configurados correctamente."
echo
echo "Puedes acceder al sitio web en:"
echo "  - http://www.miweb.com (redirige a HTTPS)"
echo "  - https://www.miweb.com"
echo "  - http://miweb.com (redirige a HTTPS)"
echo "  - https://miweb.com"
echo
echo "Estado del servicio Apache:"
sudo systemctl status apache2 --no-pager -l

echo
echo "¡Configuración exitosa!"
