# Configuración del Virtual Host SSL para www.miweb.com
# Trabajo Final - Máquinas Virtuales y Servidores Web
# Archivo: /etc/apache2/sites-available/miweb-ssl.conf

<VirtualHost *:443>
    # Configuración básica del servidor
    ServerName www.miweb.com
    ServerAlias miweb.com
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/webs/miweb
    
    # Configuración SSL
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/miweb.crt
    SSLCertificateKeyFile /etc/ssl/private/miweb.key
    
    # Configuración de protocolos SSL/TLS
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder off
    SSLSessionTickets off
    
    # Configuración de logs SSL
    ErrorLog ${APACHE_LOG_DIR}/miweb_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/miweb_ssl_access.log combined
    
    # Log específico para SSL
    CustomLog ${APACHE_LOG_DIR}/miweb_ssl_request.log \
              "%t %h %{SSL_PROTOCOL}x %{SSL_CIPHER}x \"%r\" %b"
    
    # Configuración del directorio principal
    <Directory /var/www/webs/miweb>
        # Opciones de seguridad
        Options -Indexes +FollowSymLinks -ExecCGI
        AllowOverride All
        Require all granted
        
        # Headers de seguridad mejorados para HTTPS
        Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';"
        
        # Prevenir acceso a archivos de configuración
        <Files ".ht*">
            Require all denied
        </Files>
        
        # Prevenir acceso a archivos de backup y temporales
        <FilesMatch "\.(bak|backup|old|orig|save|swp|tmp|log)$">
            Require all denied
        </FilesMatch>
        
        # Configuración de caché para recursos estáticos
        <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
            ExpiresActive On
            ExpiresDefault "access plus 1 month"
            Header append Cache-Control "public, immutable"
        </FilesMatch>
        
        # Configuración para archivos HTML
        <FilesMatch "\.(html|htm)$">
            ExpiresActive On
            ExpiresDefault "access plus 1 hour"
            Header set Cache-Control "public, must-revalidate"
        </FilesMatch>
    </Directory>
    
    # Configuración adicional de seguridad SSL
    SSLUseStapling On
    SSLStaplingCache "shmcb:logs/ssl_stapling(32768)"
    
    # Configuración de compresión para HTTPS
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
        
        # No comprimir imágenes ya comprimidas
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png|ico)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </IfModule>
    
    # Configuración de límites de tiempo
    Timeout 60
    KeepAliveTimeout 5
    
    # Configuración adicional para prevenir ataques
    <IfModule mod_evasive24.c>
        DOSHashTableSize    2048
        DOSPageCount        10
        DOSSiteCount        50
        DOSPageInterval     1
        DOSSiteInterval     1
        DOSBlockingPeriod   600
    </IfModule>
</VirtualHost>

# Configuración global SSL adicional
# Estas directivas se pueden incluir en ssl.conf

# Configuración de sesiones SSL
SSLSessionCache shmcb:/var/cache/mod_ssl/scache(512000)
SSLSessionCacheTimeout 300

# Configuración de protocolos SSL globales
SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384

# Configuración de OCSP Stapling
SSLUseStapling On
SSLStaplingResponderTimeout 5
SSLStaplingReturnResponderErrors off
